<?php
/**
 * User Creation Page - Company Admin
 * Allows company administrators to create new users within their company
 */

define('ADMIN_ACCESS', true);
require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../../classes/User.php';
require_once '../shared/admin_config.php';

// Require login and company admin privileges
requireLogin();
requireAdminAccess('company');

$database = new Database();
$db = $database->getConnection();
$user = new User($db);

$error_message = '';
$success_message = '';

// Get current user's company ID
$company_id = $_SESSION['company_id'];

// Get company information
$query = "SELECT company_name, company_code FROM companies WHERE id = ?";
$stmt = $db->prepare($query);
$stmt->execute([$company_id]);
$company_info = $stmt->fetch();

// Available modules for user permissions
$available_modules = [
    'invoices' => 'Commercial Invoices',
    'freight' => 'Freight Management', 
    'tariff' => 'Customs Tariff',
    'accounting' => 'Trade Finance',
    'backoffice' => 'Back Office'
];

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        $error_message = 'Invalid security token. Please try again.';
    } else {
        $name = trim($_POST['name'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        $role = $_POST['role'] ?? 'user';
        $selected_modules = $_POST['modules'] ?? [];
        
        // Validate required fields
        if (empty($name) || empty($email) || empty($password)) {
            $error_message = 'Name, email, and password are required.';
        } elseif ($password !== $confirm_password) {
            $error_message = 'Passwords do not match.';
        } elseif (strlen($password) < 6) {
            $error_message = 'Password must be at least 6 characters long.';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error_message = 'Please enter a valid email address.';
        } else {
            try {
                $db->beginTransaction();
                
                // Check if email is already in use within the company
                $check_query = "SELECT id FROM users WHERE email = ? AND company_id = ?";
                $check_stmt = $db->prepare($check_query);
                $check_stmt->execute([$email, $company_id]);
                
                if ($check_stmt->fetchColumn()) {
                    $error_message = 'This email address is already in use by another user in your company.';
                } else {
                    // Hash the password
                    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                    
                    // Insert new user
                    $insert_query = "INSERT INTO users (name, email, password, role, company_id, status, created_by, created_at) 
                                     VALUES (?, ?, ?, ?, ?, 'active', ?, NOW())";
                    $insert_stmt = $db->prepare($insert_query);
                    $insert_stmt->execute([$name, $email, $hashed_password, $role, $company_id, $_SESSION['user_id']]);
                    
                    $new_user_id = $db->lastInsertId();
                    
                    // Add module permissions
                    if (!empty($selected_modules)) {
                        $module_query = "INSERT INTO user_rights (user_id, module_name) VALUES (?, ?)";
                        $module_stmt = $db->prepare($module_query);
                        
                        foreach ($selected_modules as $module) {
                            if (array_key_exists($module, $available_modules)) {
                                $module_stmt->execute([$new_user_id, $module]);
                            }
                        }
                    }
                    
                    $db->commit();
                    
                    // Log activity
                    logActivity('user_created', "New user created: {$name} ({$email})", $_SESSION['user_id'], $company_id);
                    
                    $success_message = "User '{$name}' has been successfully created.";
                    
                    // Clear form data
                    $_POST = [];
                }
                
            } catch (PDOException $e) {
                $db->rollBack();
                if ($e->getCode() == 23000) {
                    $error_message = 'This email address is already in use.';
                } else {
                    $error_message = 'Database error occurred. Please try again.';
                }
            }
        }
    }
}

$csrf_token = generateCSRFToken();
$page_title = "Create New User";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo APP_NAME; ?></title>
    <link rel="icon" type="image/x-icon" href="../../assets/img/favicon.ico">
    <?php echo generateAdminCSS('company'); ?>
    <style>
        .form-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .form-section {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(43, 94, 95, 0.08);
        }
        
        .section-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2B5E5F;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            display: block;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
        }
        
        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.2s;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #2B5E5F;
        }
        
        .form-select {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 1rem;
            background: white;
        }
        
        .checkbox-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem;
            background: #f8fafa;
            border-radius: 8px;
            border: 2px solid transparent;
            transition: all 0.2s;
        }
        
        .checkbox-item:hover {
            background: #f0f4f4;
            border-color: #2B5E5F;
        }
        
        .checkbox-item input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #2B5E5F;
        }
        
        .checkbox-label {
            font-weight: 500;
            color: #374151;
            cursor: pointer;
        }
        
        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 2px solid #f0f0f0;
        }
        
        .btn-primary {
            background: #2B5E5F;
            color: white;
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .btn-primary:hover {
            background: #1a4344;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: background 0.2s;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .password-requirements {
            font-size: 0.85rem;
            color: #666;
            margin-top: 0.5rem;
        }
        
        .company-info {
            background: #f0f4f4;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
        }
        
        .company-info h3 {
            color: #2B5E5F;
            margin-bottom: 0.5rem;
        }
        
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .checkbox-group {
                grid-template-columns: 1fr;
            }
            
            .form-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <?php echo generateAdminHeader($page_title, 'company', 'users.php'); ?>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">Create New User</h1>
            <p class="page-subtitle">Add a new user to <?php echo htmlspecialchars($company_info['company_name']); ?></p>
        </div>

        <?php if ($error_message): ?>
            <div class="alert alert-error">
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>

        <?php if ($success_message): ?>
            <div class="alert alert-success">
                <?php echo htmlspecialchars($success_message); ?>
                <div style="margin-top: 1rem;">
                    <a href="users.php" class="btn">View All Users</a>
                    <a href="user_create.php" class="btn btn-secondary">Create Another User</a>
                </div>
            </div>
        <?php endif; ?>

        <div class="form-container">
            <div class="company-info">
                <h3>Creating user for: <?php echo htmlspecialchars($company_info['company_name']); ?></h3>
                <p>Company Code: <?php echo htmlspecialchars($company_info['company_code']); ?></p>
            </div>

            <form method="POST" action="user_create.php">
                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">

                <div class="form-grid">
                    <!-- User Information -->
                    <div class="form-section">
                        <h2 class="section-title">User Information</h2>

                        <div class="form-group">
                            <label for="name" class="form-label">Full Name *</label>
                            <input type="text" id="name" name="name" class="form-input"
                                   value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>"
                                   required placeholder="Enter user's full name">
                        </div>

                        <div class="form-group">
                            <label for="email" class="form-label">Email Address *</label>
                            <input type="email" id="email" name="email" class="form-input"
                                   value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>"
                                   required placeholder="<EMAIL>">
                        </div>

                        <div class="form-group">
                            <label for="role" class="form-label">User Role *</label>
                            <select id="role" name="role" class="form-select" required>
                                <option value="user" <?php echo (!isset($_POST['role']) || $_POST['role'] === 'user') ? 'selected' : ''; ?>>
                                    Regular User
                                </option>
                                <option value="company_admin" <?php echo (isset($_POST['role']) && $_POST['role'] === 'company_admin') ? 'selected' : ''; ?>>
                                    Company Administrator
                                </option>
                            </select>
                            <div class="password-requirements">
                                Company Administrators can manage other users and company settings.
                            </div>
                        </div>
                    </div>

                    <!-- Password Section -->
                    <div class="form-section">
                        <h2 class="section-title">Password Setup</h2>

                        <div class="form-group">
                            <label for="password" class="form-label">Password *</label>
                            <input type="password" id="password" name="password" class="form-input"
                                   required placeholder="Enter secure password">
                            <div class="password-requirements">
                                Password must be at least 6 characters long.
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="confirm_password" class="form-label">Confirm Password *</label>
                            <input type="password" id="confirm_password" name="confirm_password" class="form-input"
                                   required placeholder="Confirm password">
                        </div>
                    </div>
                </div>

                <!-- Module Permissions -->
                <div class="form-section">
                    <h2 class="section-title">Module Access Permissions</h2>
                    <p style="color: #666; margin-bottom: 1.5rem;">
                        Select which modules this user will have access to. You can modify these permissions later.
                    </p>

                    <div class="checkbox-group">
                        <?php foreach ($available_modules as $module_key => $module_name): ?>
                            <div class="checkbox-item">
                                <input type="checkbox" id="module_<?php echo $module_key; ?>"
                                       name="modules[]" value="<?php echo $module_key; ?>"
                                       <?php echo (isset($_POST['modules']) && in_array($module_key, $_POST['modules'])) ? 'checked' : ''; ?>>
                                <label for="module_<?php echo $module_key; ?>" class="checkbox-label">
                                    <?php echo htmlspecialchars($module_name); ?>
                                </label>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <div class="form-actions">
                    <a href="users.php" class="btn-secondary">Cancel</a>
                    <button type="submit" class="btn-primary">Create User</button>
                </div>
            </form>
        </div>
    </main>

    <script>
        // Password confirmation validation
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;

            if (password !== confirmPassword) {
                this.setCustomValidity('Passwords do not match');
            } else {
                this.setCustomValidity('');
            }
        });

        // Password strength indicator
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const confirmPassword = document.getElementById('confirm_password');

            if (confirmPassword.value && password !== confirmPassword.value) {
                confirmPassword.setCustomValidity('Passwords do not match');
            } else {
                confirmPassword.setCustomValidity('');
            }
        });
    </script>
</body>
</html>
