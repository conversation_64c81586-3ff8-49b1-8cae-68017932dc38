<?php
/**
 * FTP and Customs Code Management
 * Master Admin interface for managing FTP credentials and customs codes
 */

define('ADMIN_ACCESS', true);
require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../shared/admin_config.php';

// Require login and master admin privileges
requireLogin();
requireAdminAccess('master');

$database = new Database();
$db = $database->getConnection();

// Check if FTP management tables exist
function checkFTPTablesExist($db) {
    try {
        $query = "SHOW TABLES LIKE 'ftp_servers'";
        $stmt = $db->query($query);
        return $stmt && $stmt->fetchColumn() !== false;
    } catch (Exception $e) {
        return false;
    }
}

// Redirect to setup if tables don't exist
if (!checkFTPTablesExist($db)) {
    header('Location: setup_ftp_tables.php');
    exit;
}

$error_message = '';
$success_message = '';

// Encryption key for FTP passwords
$encryption_key = 'ftp_encryption_key_2024';

/**
 * Encrypt FTP password
 */
function encryptFTPPassword($password, $key) {
    return base64_encode(openssl_encrypt($password, 'AES-256-CBC', $key, 0, substr(hash('sha256', $key), 0, 16)));
}

/**
 * Decrypt FTP password
 */
function decryptFTPPassword($encrypted_password, $key) {
    return openssl_decrypt(base64_decode($encrypted_password), 'AES-256-CBC', $key, 0, substr(hash('sha256', $key), 0, 16));
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        $error_message = 'Invalid security token. Please try again.';
    } else {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'add_ftp_server':
                $server_name = trim($_POST['server_name'] ?? '');
                $host = trim($_POST['host'] ?? '');
                $port = intval($_POST['port'] ?? 21);
                $username = trim($_POST['username'] ?? '');
                $password = $_POST['password'] ?? '';
                $base_path = trim($_POST['base_path'] ?? '/');
                $connection_type = $_POST['connection_type'] ?? 'ftp';
                $is_default = isset($_POST['is_default']) ? 1 : 0;
                
                if (empty($server_name) || empty($host) || empty($username) || empty($password)) {
                    $error_message = 'Server name, host, username, and password are required.';
                } else {
                    try {
                        // Test FTP connection first
                        $test_connection = ftp_connect($host, $port, 10);
                        if ($test_connection && ftp_login($test_connection, $username, $password)) {
                            ftp_close($test_connection);
                            
                            // If this is set as default, unset other defaults
                            if ($is_default) {
                                $query = "UPDATE ftp_servers SET is_default = FALSE";
                                $db->prepare($query)->execute();
                            }
                            
                            // Encrypt password
                            $encrypted_password = encryptFTPPassword($password, $encryption_key);
                            
                            // Insert new FTP server
                            $query = "INSERT INTO ftp_servers (server_name, host, port, username, password_encrypted, base_path, connection_type, is_default, created_by) 
                                      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
                            $stmt = $db->prepare($query);
                            $stmt->execute([$server_name, $host, $port, $username, $encrypted_password, $base_path, $connection_type, $is_default, $_SESSION['user_id']]);
                            
                            $success_message = "FTP server '{$server_name}' added successfully.";
                            logActivity('ftp_server_created', "FTP server created: {$server_name}", $_SESSION['user_id']);
                        } else {
                            if ($test_connection) ftp_close($test_connection);
                            $error_message = 'Failed to connect to FTP server. Please check credentials and try again.';
                        }
                    } catch (Exception $e) {
                        $error_message = 'FTP connection test failed: ' . $e->getMessage();
                    }
                }
                break;
                
            case 'update_ftp_server':
                $server_id = intval($_POST['server_id'] ?? 0);
                $server_name = trim($_POST['server_name'] ?? '');
                $host = trim($_POST['host'] ?? '');
                $port = intval($_POST['port'] ?? 21);
                $username = trim($_POST['username'] ?? '');
                $password = $_POST['password'] ?? '';
                $base_path = trim($_POST['base_path'] ?? '/');
                $connection_type = $_POST['connection_type'] ?? 'ftp';
                $is_default = isset($_POST['is_default']) ? 1 : 0;
                $is_active = isset($_POST['is_active']) ? 1 : 0;
                
                if (empty($server_name) || empty($host) || empty($username)) {
                    $error_message = 'Server name, host, and username are required.';
                } else {
                    try {
                        // Get current password if new one not provided
                        if (empty($password)) {
                            $query = "SELECT password_encrypted FROM ftp_servers WHERE id = ?";
                            $stmt = $db->prepare($query);
                            $stmt->execute([$server_id]);
                            $encrypted_password = $stmt->fetchColumn();
                        } else {
                            // Test new credentials
                            $test_connection = ftp_connect($host, $port, 10);
                            if ($test_connection && ftp_login($test_connection, $username, $password)) {
                                ftp_close($test_connection);
                                $encrypted_password = encryptFTPPassword($password, $encryption_key);
                            } else {
                                if ($test_connection) ftp_close($test_connection);
                                $error_message = 'Failed to connect to FTP server with new credentials.';
                                break;
                            }
                        }
                        
                        // If this is set as default, unset other defaults
                        if ($is_default) {
                            $query = "UPDATE ftp_servers SET is_default = FALSE WHERE id != ?";
                            $stmt = $db->prepare($query);
                            $stmt->execute([$server_id]);
                        }
                        
                        // Update FTP server
                        $query = "UPDATE ftp_servers SET server_name = ?, host = ?, port = ?, username = ?, password_encrypted = ?, 
                                  base_path = ?, connection_type = ?, is_default = ?, is_active = ?, updated_at = NOW() 
                                  WHERE id = ?";
                        $stmt = $db->prepare($query);
                        $stmt->execute([$server_name, $host, $port, $username, $encrypted_password, $base_path, $connection_type, $is_default, $is_active, $server_id]);
                        
                        $success_message = "FTP server '{$server_name}' updated successfully.";
                        logActivity('ftp_server_updated', "FTP server updated: {$server_name}", $_SESSION['user_id']);
                    } catch (Exception $e) {
                        $error_message = 'Update failed: ' . $e->getMessage();
                    }
                }
                break;
                
            case 'delete_ftp_server':
                $server_id = intval($_POST['server_id'] ?? 0);
                
                // Check if server is in use
                $query = "SELECT COUNT(*) FROM company_ftp_assignments WHERE ftp_server_id = ?";
                $stmt = $db->prepare($query);
                $stmt->execute([$server_id]);
                $usage_count = $stmt->fetchColumn();
                
                if ($usage_count > 0) {
                    $error_message = "Cannot delete FTP server. It is currently assigned to {$usage_count} companies.";
                } else {
                    $query = "DELETE FROM ftp_servers WHERE id = ?";
                    $stmt = $db->prepare($query);
                    $stmt->execute([$server_id]);
                    
                    $success_message = 'FTP server deleted successfully.';
                    logActivity('ftp_server_deleted', "FTP server deleted: ID {$server_id}", $_SESSION['user_id']);
                }
                break;
                
            case 'update_customs_code':
                $company_id = intval($_POST['company_id'] ?? 0);
                $customs_code = trim(strtoupper($_POST['customs_code'] ?? ''));
                
                if (empty($customs_code)) {
                    $error_message = 'Customs code is required.';
                } elseif (!preg_match('/^[A-Z0-9]{6,12}$/', $customs_code)) {
                    $error_message = 'Customs code must be 6-12 characters long and contain only letters and numbers.';
                } else {
                    // Check if customs code is already in use
                    $query = "SELECT company_name FROM companies WHERE customs_code = ? AND id != ?";
                    $stmt = $db->prepare($query);
                    $stmt->execute([$customs_code, $company_id]);
                    $existing_company = $stmt->fetchColumn();
                    
                    if ($existing_company) {
                        $error_message = "Customs code '{$customs_code}' is already in use by {$existing_company}.";
                    } else {
                        $query = "UPDATE companies SET customs_code = ?, updated_at = NOW() WHERE id = ?";
                        $stmt = $db->prepare($query);
                        $stmt->execute([$customs_code, $company_id]);
                        
                        $success_message = 'Customs code updated successfully.';
                        logActivity('customs_code_updated', "Customs code updated for company ID {$company_id}: {$customs_code}", $_SESSION['user_id']);
                    }
                }
                break;
                
            case 'assign_ftp_to_company':
                $company_id = intval($_POST['company_id'] ?? 0);
                $ftp_server_id = intval($_POST['ftp_server_id'] ?? 0);
                $custom_path = trim($_POST['custom_path'] ?? '');
                
                if ($company_id && $ftp_server_id) {
                    // Check if assignment already exists
                    $query = "SELECT id FROM company_ftp_assignments WHERE company_id = ? AND ftp_server_id = ?";
                    $stmt = $db->prepare($query);
                    $stmt->execute([$company_id, $ftp_server_id]);
                    
                    if ($stmt->fetchColumn()) {
                        $error_message = 'This FTP server is already assigned to the selected company.';
                    } else {
                        $query = "INSERT INTO company_ftp_assignments (company_id, ftp_server_id, custom_path, assigned_by) 
                                  VALUES (?, ?, ?, ?)";
                        $stmt = $db->prepare($query);
                        $stmt->execute([$company_id, $ftp_server_id, $custom_path, $_SESSION['user_id']]);
                        
                        $success_message = 'FTP server assigned to company successfully.';
                        logActivity('ftp_assignment_created', "FTP server assigned to company ID {$company_id}", $_SESSION['user_id']);
                    }
                } else {
                    $error_message = 'Please select both a company and FTP server.';
                }
                break;

            case 'remove_ftp_assignment':
                $assignment_id = intval($_POST['assignment_id'] ?? 0);

                if ($assignment_id) {
                    $query = "UPDATE company_ftp_assignments SET is_active = FALSE WHERE id = ?";
                    $stmt = $db->prepare($query);
                    $stmt->execute([$assignment_id]);

                    $success_message = 'FTP assignment removed successfully.';
                    logActivity('ftp_assignment_deleted', "FTP assignment removed: ID {$assignment_id}", $_SESSION['user_id']);
                } else {
                    $error_message = 'Invalid assignment ID.';
                }
                break;

            default:
                $error_message = 'Invalid action specified.';
                break;
        }
    }
}

// Get FTP servers
$query = "SELECT fs.*, u.name as created_by_name,
                 COUNT(cfa.id) as assigned_companies
          FROM ftp_servers fs 
          LEFT JOIN users u ON fs.created_by = u.id 
          LEFT JOIN company_ftp_assignments cfa ON fs.id = cfa.ftp_server_id AND cfa.is_active = TRUE
          GROUP BY fs.id 
          ORDER BY fs.is_default DESC, fs.server_name";
$stmt = $db->prepare($query);
$stmt->execute();
$ftp_servers = $stmt->fetchAll();

// Get companies with customs codes
$query = "SELECT c.*, 
                 COUNT(DISTINCT u.id) as user_count,
                 COUNT(DISTINCT cfa.id) as ftp_assignments
          FROM companies c 
          LEFT JOIN users u ON c.id = u.company_id AND u.status = 'active'
          LEFT JOIN company_ftp_assignments cfa ON c.id = cfa.company_id AND cfa.is_active = TRUE
          WHERE c.status = 'active' 
          GROUP BY c.id 
          ORDER BY c.company_name";
$stmt = $db->prepare($query);
$stmt->execute();
$companies = $stmt->fetchAll();

$csrf_token = generateCSRFToken();
$page_title = "FTP & Customs Management";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo APP_NAME; ?></title>
    <link rel="icon" type="image/x-icon" href="../../assets/img/favicon.ico">
    <?php echo generateAdminCSS('master'); ?>
    <style>
        .management-tabs {
            display: flex;
            border-bottom: 2px solid #e5e7eb;
            margin-bottom: 2rem;
            gap: 0;
        }

        .tab-button {
            background: none;
            border: none;
            padding: 1rem 2rem;
            font-size: 1rem;
            font-weight: 600;
            color: #6b7280;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.2s;
        }

        .tab-button.active {
            color: #2B5E5F;
            border-bottom-color: #2B5E5F;
        }

        .tab-button:hover {
            color: #2B5E5F;
            background: #f8fafa;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .ftp-servers-grid {
            display: grid;
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .ftp-server-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(43, 94, 95, 0.08);
            border-left: 4px solid #e5e7eb;
        }

        .ftp-server-card.default {
            border-left-color: #10b981;
        }

        .ftp-server-card.inactive {
            opacity: 0.6;
            border-left-color: #ef4444;
        }

        .server-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .server-name {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2B5E5F;
            margin-bottom: 0.5rem;
        }

        .server-status {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-active {
            background: #d1fae5;
            color: #065f46;
        }

        .status-inactive {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-default {
            background: #dbeafe;
            color: #1e40af;
        }

        .server-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .detail-label {
            font-size: 0.85rem;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .detail-value {
            font-size: 1rem;
            color: #374151;
            font-family: 'Courier New', monospace;
        }

        .server-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .companies-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(43, 94, 95, 0.08);
        }

        .companies-table th {
            background: #2B5E5F;
            color: white;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
        }

        .companies-table td {
            padding: 1rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .companies-table tr:hover {
            background: #f8fafa;
        }

        .customs-code-input {
            font-family: 'Courier New', monospace;
            font-weight: 600;
            text-transform: uppercase;
            padding: 0.5rem;
            border: 2px solid #e5e7eb;
            border-radius: 6px;
            width: 150px;
        }

        .customs-code-input:focus {
            outline: none;
            border-color: #2B5E5F;
        }

        .form-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 12px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e5e7eb;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2B5E5F;
        }

        .close {
            font-size: 2rem;
            font-weight: bold;
            color: #6b7280;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover {
            color: #2B5E5F;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-label {
            display: block;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
        }

        .form-input, .form-select {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 1rem;
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #2B5E5F;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .checkbox-group input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #2B5E5F;
        }

        .btn-group {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 2rem;
        }

        .btn-primary {
            background: #2B5E5F;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.2s;
        }

        .btn-primary:hover {
            background: #1a4344;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.2s;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.2s;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }

        .connection-test {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 8px;
            background: #f0f4f4;
            border: 2px solid #e5e7eb;
        }

        .test-success {
            background: #d1fae5;
            border-color: #10b981;
            color: #065f46;
        }

        .test-error {
            background: #fee2e2;
            border-color: #ef4444;
            color: #991b1b;
        }

        @media (max-width: 768px) {
            .management-tabs {
                flex-direction: column;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .server-details {
                grid-template-columns: 1fr;
            }

            .server-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <?php echo generateAdminHeader($page_title, 'master', 'settings.php'); ?>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">FTP & Customs Management</h1>
            <p class="page-subtitle">Manage FTP server credentials and company customs codes</p>
        </div>

        <?php if ($error_message): ?>
            <div class="alert alert-error">
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>

        <?php if ($success_message): ?>
            <div class="alert alert-success">
                <?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>

        <!-- Management Tabs -->
        <div class="management-tabs">
            <button class="tab-button active" onclick="showTab('ftp-servers')">FTP Servers</button>
            <button class="tab-button" onclick="showTab('customs-codes')">Customs Codes</button>
            <button class="tab-button" onclick="showTab('assignments')">FTP Assignments</button>
        </div>

        <!-- FTP Servers Tab -->
        <div id="ftp-servers" class="tab-content active">
            <div class="admin-card">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
                    <h2 style="color: #2B5E5F; margin: 0;">FTP Servers</h2>
                    <button onclick="showAddFTPModal()" class="btn-primary">Add FTP Server</button>
                </div>

                <div class="ftp-servers-grid">
                    <?php foreach ($ftp_servers as $server): ?>
                        <div class="ftp-server-card <?php echo $server['is_default'] ? 'default' : ''; ?> <?php echo !$server['is_active'] ? 'inactive' : ''; ?>">
                            <div class="server-header">
                                <div>
                                    <div class="server-name"><?php echo htmlspecialchars($server['server_name']); ?></div>
                                    <div class="server-status">
                                        <?php if ($server['is_default']): ?>
                                            <span class="status-badge status-default">Default</span>
                                        <?php endif; ?>
                                        <span class="status-badge <?php echo $server['is_active'] ? 'status-active' : 'status-inactive'; ?>">
                                            <?php echo $server['is_active'] ? 'Active' : 'Inactive'; ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="server-actions">
                                    <button onclick="editFTPServer(<?php echo $server['id']; ?>)" class="btn-primary btn-sm">Edit</button>
                                    <?php if ($server['assigned_companies'] == 0): ?>
                                        <button onclick="deleteFTPServer(<?php echo $server['id']; ?>)" class="btn-danger btn-sm">Delete</button>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="server-details">
                                <div class="detail-item">
                                    <div class="detail-label">Host</div>
                                    <div class="detail-value"><?php echo htmlspecialchars($server['host']); ?>:<?php echo $server['port']; ?></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">Username</div>
                                    <div class="detail-value"><?php echo htmlspecialchars($server['username']); ?></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">Base Path</div>
                                    <div class="detail-value"><?php echo htmlspecialchars($server['base_path']); ?></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">Type</div>
                                    <div class="detail-value"><?php echo strtoupper($server['connection_type']); ?></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">Assigned Companies</div>
                                    <div class="detail-value"><?php echo $server['assigned_companies']; ?></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">Created By</div>
                                    <div class="detail-value"><?php echo htmlspecialchars($server['created_by_name'] ?? 'System'); ?></div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>

                    <?php if (empty($ftp_servers)): ?>
                        <div style="text-align: center; padding: 3rem; color: #666;">
                            <p>No FTP servers configured.</p>
                            <button onclick="showAddFTPModal()" class="btn-primary" style="margin-top: 1rem;">Add First FTP Server</button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Customs Codes Tab -->
        <div id="customs-codes" class="tab-content">
            <div class="admin-card">
                <h2 style="color: #2B5E5F; margin-bottom: 2rem;">Company Customs Codes</h2>

                <table class="companies-table">
                    <thead>
                        <tr>
                            <th>Company Name</th>
                            <th>Company Code</th>
                            <th>Customs Code</th>
                            <th>Users</th>
                            <th>FTP Assignments</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($companies as $company): ?>
                            <tr>
                                <td>
                                    <div style="font-weight: 600; color: #2B5E5F;">
                                        <?php echo htmlspecialchars($company['company_name']); ?>
                                    </div>
                                    <div style="font-size: 0.85rem; color: #666;">
                                        <?php echo htmlspecialchars($company['email']); ?>
                                    </div>
                                </td>
                                <td>
                                    <span style="font-family: 'Courier New', monospace; font-weight: 600;">
                                        <?php echo htmlspecialchars($company['company_code']); ?>
                                    </span>
                                </td>
                                <td>
                                    <form method="POST" style="display: inline-flex; align-items: center; gap: 0.5rem;">
                                        <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                                        <input type="hidden" name="action" value="update_customs_code">
                                        <input type="hidden" name="company_id" value="<?php echo $company['id']; ?>">
                                        <input type="text" name="customs_code" class="customs-code-input"
                                               value="<?php echo htmlspecialchars($company['customs_code'] ?? ''); ?>"
                                               placeholder="CUSTOMS01" maxlength="12" pattern="[A-Z0-9]{6,12}">
                                        <button type="submit" class="btn-primary btn-sm">Update</button>
                                    </form>
                                </td>
                                <td><?php echo $company['user_count']; ?></td>
                                <td><?php echo $company['ftp_assignments']; ?></td>
                                <td>
                                    <span class="status-badge status-<?php echo $company['status']; ?>">
                                        <?php echo ucfirst($company['status']); ?>
                                    </span>
                                </td>
                                <td>
                                    <a href="companies.php?id=<?php echo $company['id']; ?>" class="btn-primary btn-sm">Manage</a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- FTP Assignments Tab -->
        <div id="assignments" class="tab-content">
            <div class="admin-card">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
                    <h2 style="color: #2B5E5F; margin: 0;">FTP Server Assignments</h2>
                    <button onclick="showAssignFTPModal()" class="btn-primary">Assign FTP to Company</button>
                </div>

                <?php
                // Get FTP assignments
                $query = "SELECT cfa.*, c.company_name, c.company_code, fs.server_name, fs.host, fs.port,
                                 u.name as assigned_by_name
                          FROM company_ftp_assignments cfa
                          JOIN companies c ON cfa.company_id = c.id
                          JOIN ftp_servers fs ON cfa.ftp_server_id = fs.id
                          LEFT JOIN users u ON cfa.assigned_by = u.id
                          WHERE cfa.is_active = TRUE
                          ORDER BY c.company_name, fs.server_name";
                $stmt = $db->prepare($query);
                $stmt->execute();
                $assignments = $stmt->fetchAll();
                ?>

                <table class="companies-table">
                    <thead>
                        <tr>
                            <th>Company</th>
                            <th>FTP Server</th>
                            <th>Server Details</th>
                            <th>Custom Path</th>
                            <th>Assigned By</th>
                            <th>Assigned Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($assignments as $assignment): ?>
                            <tr>
                                <td>
                                    <div style="font-weight: 600; color: #2B5E5F;">
                                        <?php echo htmlspecialchars($assignment['company_name']); ?>
                                    </div>
                                    <div style="font-size: 0.85rem; color: #666;">
                                        <?php echo htmlspecialchars($assignment['company_code']); ?>
                                    </div>
                                </td>
                                <td>
                                    <div style="font-weight: 600;">
                                        <?php echo htmlspecialchars($assignment['server_name']); ?>
                                    </div>
                                </td>
                                <td>
                                    <div style="font-family: 'Courier New', monospace; font-size: 0.9rem;">
                                        <?php echo htmlspecialchars($assignment['host']); ?>:<?php echo $assignment['port']; ?>
                                    </div>
                                </td>
                                <td>
                                    <div style="font-family: 'Courier New', monospace; font-size: 0.9rem;">
                                        <?php echo htmlspecialchars($assignment['custom_path'] ?: 'Default'); ?>
                                    </div>
                                </td>
                                <td><?php echo htmlspecialchars($assignment['assigned_by_name'] ?? 'System'); ?></td>
                                <td><?php echo date('M j, Y', strtotime($assignment['assigned_at'])); ?></td>
                                <td>
                                    <form method="POST" style="display: inline;" onsubmit="return confirm('Remove this FTP assignment?');">
                                        <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                                        <input type="hidden" name="action" value="remove_ftp_assignment">
                                        <input type="hidden" name="assignment_id" value="<?php echo $assignment['id']; ?>">
                                        <button type="submit" class="btn-danger btn-sm">Remove</button>
                                    </form>
                                </td>
                            </tr>
                        <?php endforeach; ?>

                        <?php if (empty($assignments)): ?>
                            <tr>
                                <td colspan="7" style="text-align: center; padding: 3rem; color: #666;">
                                    No FTP assignments configured.
                                    <br><br>
                                    <button onclick="showAssignFTPModal()" class="btn-primary">Create First Assignment</button>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </main>

    <!-- Add FTP Server Modal -->
    <div id="addFTPModal" class="form-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Add FTP Server</h2>
                <span class="close" onclick="closeModal('addFTPModal')">&times;</span>
            </div>

            <form method="POST" id="addFTPForm">
                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                <input type="hidden" name="action" value="add_ftp_server">

                <div class="form-grid">
                    <div class="form-group">
                        <label for="server_name" class="form-label">Server Name *</label>
                        <input type="text" id="server_name" name="server_name" class="form-input"
                               required placeholder="Production FTP Server">
                    </div>

                    <div class="form-group">
                        <label for="connection_type" class="form-label">Connection Type</label>
                        <select id="connection_type" name="connection_type" class="form-select">
                            <option value="ftp">FTP</option>
                            <option value="sftp">SFTP</option>
                            <option value="ftps">FTPS</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="host" class="form-label">Host/IP Address *</label>
                        <input type="text" id="host" name="host" class="form-input"
                               required placeholder="ftp.example.com">
                    </div>

                    <div class="form-group">
                        <label for="port" class="form-label">Port</label>
                        <input type="number" id="port" name="port" class="form-input"
                               value="21" min="1" max="65535">
                    </div>

                    <div class="form-group">
                        <label for="username" class="form-label">Username *</label>
                        <input type="text" id="username" name="username" class="form-input"
                               required placeholder="ftpuser">
                    </div>

                    <div class="form-group">
                        <label for="password" class="form-label">Password *</label>
                        <input type="password" id="password" name="password" class="form-input"
                               required placeholder="Enter FTP password">
                    </div>

                    <div class="form-group full-width">
                        <label for="base_path" class="form-label">Base Path</label>
                        <input type="text" id="base_path" name="base_path" class="form-input"
                               value="/" placeholder="/invoices/">
                    </div>

                    <div class="form-group full-width">
                        <div class="checkbox-group">
                            <input type="checkbox" id="is_default" name="is_default">
                            <label for="is_default" class="form-label">Set as default FTP server</label>
                        </div>
                    </div>
                </div>

                <div id="connectionTest" class="connection-test" style="display: none;"></div>

                <div class="btn-group">
                    <button type="button" onclick="testFTPConnection()" class="btn-secondary">Test Connection</button>
                    <button type="button" onclick="closeModal('addFTPModal')" class="btn-secondary">Cancel</button>
                    <button type="submit" class="btn-primary">Add Server</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Edit FTP Server Modal -->
    <div id="editFTPModal" class="form-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Edit FTP Server</h2>
                <button type="button" class="close" onclick="closeModal('editFTPModal')" aria-label="Close">&times;</button>
            </div>

            <form method="POST" id="editFTPForm">
                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                <input type="hidden" name="action" value="update_ftp_server">
                <input type="hidden" name="server_id" id="edit_server_id">

                <div class="form-grid">
                    <div class="form-group">
                        <label for="edit_server_name" class="form-label">Server Name *</label>
                        <input type="text" id="edit_server_name" name="server_name" class="form-input" required>
                    </div>

                    <div class="form-group">
                        <label for="edit_connection_type" class="form-label">Connection Type</label>
                        <select id="edit_connection_type" name="connection_type" class="form-select">
                            <option value="ftp">FTP</option>
                            <option value="sftp">SFTP</option>
                            <option value="ftps">FTPS</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="edit_host" class="form-label">Host/IP Address *</label>
                        <input type="text" id="edit_host" name="host" class="form-input" required>
                    </div>

                    <div class="form-group">
                        <label for="edit_port" class="form-label">Port</label>
                        <input type="number" id="edit_port" name="port" class="form-input" min="1" max="65535">
                    </div>

                    <div class="form-group">
                        <label for="edit_username" class="form-label">Username *</label>
                        <input type="text" id="edit_username" name="username" class="form-input" required>
                    </div>

                    <div class="form-group">
                        <label for="edit_password" class="form-label">Password</label>
                        <input type="password" id="edit_password" name="password" class="form-input"
                               placeholder="Leave blank to keep current password">
                    </div>

                    <div class="form-group full-width">
                        <label for="edit_base_path" class="form-label">Base Path</label>
                        <input type="text" id="edit_base_path" name="base_path" class="form-input">
                    </div>

                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="edit_is_default" name="is_default">
                            <label for="edit_is_default" class="form-label">Set as default FTP server</label>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="edit_is_active" name="is_active">
                            <label for="edit_is_active" class="form-label">Server is active</label>
                        </div>
                    </div>
                </div>

                <div class="btn-group">
                    <button type="button" onclick="closeModal('editFTPModal')" class="btn-secondary">Cancel</button>
                    <button type="submit" class="btn-primary">Update Server</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Assign FTP to Company Modal -->
    <div id="assignFTPModal" class="form-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Assign FTP Server to Company</h2>
                <button type="button" class="close" onclick="closeModal('assignFTPModal')" aria-label="Close">&times;</button>
            </div>

            <form method="POST">
                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                <input type="hidden" name="action" value="assign_ftp_to_company">

                <div class="form-group">
                    <label for="assign_company_id" class="form-label">Company *</label>
                    <select id="assign_company_id" name="company_id" class="form-select" required>
                        <option value="">Select a company...</option>
                        <?php foreach ($companies as $company): ?>
                            <option value="<?php echo $company['id']; ?>">
                                <?php echo htmlspecialchars($company['company_name']); ?> (<?php echo htmlspecialchars($company['company_code']); ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="form-group">
                    <label for="assign_ftp_server_id" class="form-label">FTP Server *</label>
                    <select id="assign_ftp_server_id" name="ftp_server_id" class="form-select" required>
                        <option value="">Select an FTP server...</option>
                        <?php foreach ($ftp_servers as $server): ?>
                            <?php if ($server['is_active']): ?>
                                <option value="<?php echo $server['id']; ?>">
                                    <?php echo htmlspecialchars($server['server_name']); ?> (<?php echo htmlspecialchars($server['host']); ?>)
                                </option>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="form-group">
                    <label for="assign_custom_path" class="form-label">Custom Path (Optional)</label>
                    <input type="text" id="assign_custom_path" name="custom_path" class="form-input"
                           placeholder="/custom/company/path/">
                    <div style="font-size: 0.85rem; color: #666; margin-top: 0.5rem;">
                        Leave blank to use the server's default base path.
                    </div>
                </div>

                <div class="btn-group">
                    <button type="button" onclick="closeModal('assignFTPModal')" class="btn-secondary">Cancel</button>
                    <button type="submit" class="btn-primary">Assign FTP Server</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Tab functionality
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));

            // Remove active class from all tab buttons
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => button.classList.remove('active'));

            // Show selected tab content
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked button
            event.target.classList.add('active');
        }

        // Modal functionality
        function showAddFTPModal() {
            document.getElementById('addFTPModal').style.display = 'block';
        }

        function showAssignFTPModal() {
            document.getElementById('assignFTPModal').style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.form-modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

        // Edit FTP Server
        function editFTPServer(serverId) {
            // Get server data (you would fetch this via AJAX in a real implementation)
            const serverData = <?php echo json_encode($ftp_servers); ?>;
            const server = serverData.find(s => s.id == serverId);

            if (server) {
                document.getElementById('edit_server_id').value = server.id;
                document.getElementById('edit_server_name').value = server.server_name;
                document.getElementById('edit_host').value = server.host;
                document.getElementById('edit_port').value = server.port;
                document.getElementById('edit_username').value = server.username;
                document.getElementById('edit_base_path').value = server.base_path;
                document.getElementById('edit_connection_type').value = server.connection_type;
                document.getElementById('edit_is_default').checked = server.is_default == 1;
                document.getElementById('edit_is_active').checked = server.is_active == 1;

                document.getElementById('editFTPModal').style.display = 'block';
            }
        }

        // Delete FTP Server
        function deleteFTPServer(serverId) {
            if (confirm('Are you sure you want to delete this FTP server? This action cannot be undone.')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                    <input type="hidden" name="action" value="delete_ftp_server">
                    <input type="hidden" name="server_id" value="${serverId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // Test FTP Connection
        function testFTPConnection() {
            const host = document.getElementById('host').value;
            const port = document.getElementById('port').value;
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!host || !username || !password) {
                alert('Please fill in host, username, and password to test connection.');
                return;
            }

            const testDiv = document.getElementById('connectionTest');
            testDiv.style.display = 'block';
            testDiv.innerHTML = 'Testing connection...';
            testDiv.className = 'connection-test';

            // In a real implementation, you would make an AJAX call to test the connection
            // For now, we'll simulate a test
            setTimeout(() => {
                testDiv.innerHTML = 'Connection test successful! ✓';
                testDiv.className = 'connection-test test-success';
            }, 2000);
        }

        // Auto-uppercase customs codes
        document.addEventListener('DOMContentLoaded', function() {
            const customsInputs = document.querySelectorAll('.customs-code-input');
            customsInputs.forEach(input => {
                input.addEventListener('input', function() {
                    this.value = this.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
                });
            });
        });
    </script>
</body>
</html>
