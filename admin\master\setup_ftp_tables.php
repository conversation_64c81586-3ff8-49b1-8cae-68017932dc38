<?php
/**
 * Database Setup for FTP Management
 * One-time setup script to create FTP management tables
 */

define('ADMIN_ACCESS', true);
require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../shared/admin_config.php';

// Require login and master admin privileges
requireLogin();
requireAdminAccess('master');

$database = new Database();
$db = $database->getConnection();

$error_message = '';
$success_message = '';
$setup_complete = false;

// Check if tables already exist
function checkTablesExist($db) {
    $tables = ['ftp_servers', 'company_ftp_assignments', 'system_settings'];
    $existing_tables = [];

    foreach ($tables as $table) {
        $query = "SHOW TABLES LIKE '" . $table . "'";
        $stmt = $db->query($query);
        if ($stmt && $stmt->fetchColumn()) {
            $existing_tables[] = $table;
        }
    }

    return $existing_tables;
}

$existing_tables = checkTablesExist($db);

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['setup_tables'])) {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        $error_message = 'Invalid security token. Please try again.';
    } else {
        try {
            $db->beginTransaction();
            
            // Create FTP Servers table
            $query = "CREATE TABLE IF NOT EXISTS ftp_servers (
                id INT(11) NOT NULL AUTO_INCREMENT,
                server_name VARCHAR(255) NOT NULL,
                host VARCHAR(255) NOT NULL,
                port INT(11) NOT NULL DEFAULT 21,
                username VARCHAR(255) NOT NULL,
                password_encrypted TEXT NOT NULL,
                base_path VARCHAR(500) DEFAULT '/',
                connection_type ENUM('ftp', 'sftp', 'ftps') NOT NULL DEFAULT 'ftp',
                is_active BOOLEAN NOT NULL DEFAULT TRUE,
                is_default BOOLEAN NOT NULL DEFAULT FALSE,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                created_by INT(11) DEFAULT NULL,
                PRIMARY KEY (id),
                UNIQUE KEY unique_server_name (server_name),
                KEY idx_host (host),
                KEY idx_is_active (is_active),
                KEY idx_is_default (is_default),
                CONSTRAINT fk_ftp_servers_created_by FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $db->exec($query);
            
            // Create Company FTP Assignments table
            $query = "CREATE TABLE IF NOT EXISTS company_ftp_assignments (
                id INT(11) NOT NULL AUTO_INCREMENT,
                company_id INT(11) NOT NULL,
                ftp_server_id INT(11) NOT NULL,
                custom_path VARCHAR(500) DEFAULT NULL,
                is_active BOOLEAN NOT NULL DEFAULT TRUE,
                assigned_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                assigned_by INT(11) DEFAULT NULL,
                PRIMARY KEY (id),
                UNIQUE KEY unique_company_ftp (company_id, ftp_server_id),
                KEY idx_company_id (company_id),
                KEY idx_ftp_server_id (ftp_server_id),
                KEY idx_is_active (is_active),
                CONSTRAINT fk_company_ftp_company FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
                CONSTRAINT fk_company_ftp_server FOREIGN KEY (ftp_server_id) REFERENCES ftp_servers(id) ON DELETE CASCADE,
                CONSTRAINT fk_company_ftp_assigned_by FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $db->exec($query);
            
            // Create System Settings table
            $query = "CREATE TABLE IF NOT EXISTS system_settings (
                id INT(11) NOT NULL AUTO_INCREMENT,
                setting_key VARCHAR(255) NOT NULL,
                setting_value TEXT,
                setting_type ENUM('string', 'integer', 'boolean', 'json', 'encrypted') NOT NULL DEFAULT 'string',
                description TEXT,
                is_editable BOOLEAN NOT NULL DEFAULT TRUE,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                updated_by INT(11) DEFAULT NULL,
                PRIMARY KEY (id),
                UNIQUE KEY unique_setting_key (setting_key),
                KEY idx_setting_key (setting_key),
                KEY idx_setting_type (setting_type),
                CONSTRAINT fk_system_settings_updated_by FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $db->exec($query);
            
            // Add FTP-related columns to companies table if they don't exist
            $columns_to_add = [
                'ftp_host' => 'VARCHAR(255) DEFAULT NULL',
                'ftp_username' => 'VARCHAR(255) DEFAULT NULL', 
                'ftp_password' => 'VARCHAR(255) DEFAULT NULL',
                'ftp_port' => 'INT(11) DEFAULT 21'
            ];
            
            foreach ($columns_to_add as $column => $definition) {
                $check_query = "SHOW COLUMNS FROM companies LIKE ?";
                $stmt = $db->prepare($check_query);
                $stmt->execute([$column]);
                
                if (!$stmt->fetchColumn()) {
                    $alter_query = "ALTER TABLE companies ADD COLUMN {$column} {$definition}";
                    $db->exec($alter_query);
                }
            }
            
            // Add indexes to companies table
            $indexes_to_add = [
                'idx_customs_code' => 'customs_code',
                'idx_status' => 'status'
            ];
            
            foreach ($indexes_to_add as $index_name => $column) {
                $check_index = "SHOW INDEX FROM companies WHERE Key_name = ?";
                $stmt = $db->prepare($check_index);
                $stmt->execute([$index_name]);
                
                if (!$stmt->fetchColumn()) {
                    $index_query = "ALTER TABLE companies ADD INDEX {$index_name} ({$column})";
                    $db->exec($index_query);
                }
            }
            
            // Update activity_logs enum if needed
            $query = "ALTER TABLE activity_logs MODIFY COLUMN action_type ENUM(
                'login', 'logout', 'company_created', 'user_created', 'user_updated', 'user_deleted', 
                'payment_made', 'payment_failed', 'invoice_downloaded', 'permission_changed', 
                'system_error', 'ftp_server_created', 'ftp_server_updated', 'ftp_server_deleted',
                'ftp_assignment_created', 'ftp_assignment_updated', 'ftp_assignment_deleted',
                'customs_code_updated', 'system_settings_updated'
            ) NOT NULL";
            $db->exec($query);
            
            // Insert default system settings
            $default_settings = [
                ['ftp_encryption_key', 'ftp_encryption_key_2024', 'encrypted', 'Encryption key for FTP passwords', 0],
                ['default_ftp_timeout', '30', 'integer', 'Default FTP connection timeout in seconds', 1],
                ['customs_code_format', '^[A-Z0-9]{6,12}$', 'string', 'Regex pattern for customs code validation', 1],
                ['max_ftp_connections', '10', 'integer', 'Maximum concurrent FTP connections', 1],
                ['ftp_passive_mode', 'true', 'boolean', 'Use passive mode for FTP connections', 1]
            ];
            
            foreach ($default_settings as $setting) {
                $check_query = "SELECT id FROM system_settings WHERE setting_key = ?";
                $stmt = $db->prepare($check_query);
                $stmt->execute([$setting[0]]);
                
                if (!$stmt->fetchColumn()) {
                    $insert_query = "INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_editable) VALUES (?, ?, ?, ?, ?)";
                    $stmt = $db->prepare($insert_query);
                    $stmt->execute($setting);
                }
            }
            
            // Insert default FTP server if none exists
            $check_query = "SELECT COUNT(*) FROM ftp_servers";
            $stmt = $db->prepare($check_query);
            $stmt->execute();
            
            if ($stmt->fetchColumn() == 0) {
                $encryption_key = 'ftp_encryption_key_2024';
                $default_password = 'defaultpass';
                $encrypted_password = base64_encode(openssl_encrypt($default_password, 'AES-256-CBC', $encryption_key, 0, substr(hash('sha256', $encryption_key), 0, 16)));
                
                $insert_query = "INSERT INTO ftp_servers (server_name, host, port, username, password_encrypted, base_path, is_default, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
                $stmt = $db->prepare($insert_query);
                $stmt->execute(['Default FTP Server', 'localhost', 21, 'ftpuser', $encrypted_password, '/invoices/', 1, $_SESSION['user_id']]);
            }
            
            $db->commit();
            $success_message = 'FTP management tables created successfully! You can now use the FTP & Customs management features.';
            $setup_complete = true;
            
            logActivity('system_settings_updated', 'FTP management tables created', $_SESSION['user_id']);
            
        } catch (Exception $e) {
            $db->rollback();
            $error_message = 'Setup failed: ' . $e->getMessage();
        }
    }
}

$csrf_token = generateCSRFToken();
$page_title = "FTP Management Setup";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo APP_NAME; ?></title>
    <link rel="icon" type="image/x-icon" href="../../assets/img/favicon.ico">
    <?php echo generateAdminCSS('master'); ?>
    <style>
        .setup-container {
            max-width: 800px;
            margin: 2rem auto;
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(43, 94, 95, 0.08);
        }
        
        .setup-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .setup-title {
            font-size: 2rem;
            font-weight: 600;
            color: #2B5E5F;
            margin-bottom: 0.5rem;
        }
        
        .setup-subtitle {
            color: #666;
            font-size: 1.1rem;
        }
        
        .tables-status {
            background: #f8fafa;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .status-label {
            font-weight: 600;
            color: #374151;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-exists {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-missing {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .setup-actions {
            text-align: center;
            margin-top: 2rem;
        }
        
        .btn-setup {
            background: #2B5E5F;
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .btn-setup:hover {
            background: #1a4344;
        }
        
        .btn-setup:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        
        .setup-complete {
            text-align: center;
            padding: 2rem;
            background: #d1fae5;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        
        .complete-icon {
            font-size: 3rem;
            color: #10b981;
            margin-bottom: 1rem;
        }
        
        .complete-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #065f46;
            margin-bottom: 0.5rem;
        }
        
        .complete-message {
            color: #047857;
            font-size: 1.1rem;
        }
        
        .navigation-links {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 2rem;
        }
        
        .nav-link {
            background: #2B5E5F;
            color: white;
            text-decoration: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            transition: background 0.2s;
        }
        
        .nav-link:hover {
            background: #1a4344;
        }
    </style>
</head>
<body>
    <?php echo generateAdminHeader($page_title, 'master', 'settings.php'); ?>

    <main class="main-content">
        <div class="setup-container">
            <div class="setup-header">
                <h1 class="setup-title">FTP Management Setup</h1>
                <p class="setup-subtitle">Initialize database tables for FTP and customs code management</p>
            </div>

            <?php if ($error_message): ?>
                <div class="alert alert-error">
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <?php if ($success_message): ?>
                <div class="alert alert-success">
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <?php if ($setup_complete): ?>
                <div class="setup-complete">
                    <div class="complete-icon">✅</div>
                    <h2 class="complete-title">Setup Complete!</h2>
                    <p class="complete-message">FTP management tables have been successfully created and configured.</p>
                </div>

                <div class="navigation-links">
                    <a href="ftp_management.php" class="nav-link">Go to FTP Management</a>
                    <a href="dashboard.php" class="nav-link">Return to Dashboard</a>
                </div>
            <?php else: ?>
                <div class="tables-status">
                    <h3 style="color: #2B5E5F; margin-bottom: 1rem;">Database Tables Status</h3>

                    <?php
                    $required_tables = [
                        'ftp_servers' => 'FTP Servers Configuration',
                        'company_ftp_assignments' => 'Company FTP Assignments',
                        'system_settings' => 'System Settings'
                    ];

                    foreach ($required_tables as $table => $description):
                        $exists = in_array($table, $existing_tables);
                    ?>
                        <div class="status-item">
                            <div>
                                <div class="status-label"><?php echo $description; ?></div>
                                <div style="font-size: 0.9rem; color: #666; font-family: 'Courier New', monospace;">
                                    <?php echo $table; ?>
                                </div>
                            </div>
                            <span class="status-badge <?php echo $exists ? 'status-exists' : 'status-missing'; ?>">
                                <?php echo $exists ? 'Exists' : 'Missing'; ?>
                            </span>
                        </div>
                    <?php endforeach; ?>
                </div>

                <?php if (count($existing_tables) === count($required_tables)): ?>
                    <div style="text-align: center; padding: 2rem; background: #dbeafe; border-radius: 8px; margin-bottom: 2rem;">
                        <h3 style="color: #1e40af; margin-bottom: 1rem;">Tables Already Exist</h3>
                        <p style="color: #1e3a8a;">All required tables are already present in the database. You can proceed to use the FTP management features.</p>

                        <div class="navigation-links" style="margin-top: 1.5rem;">
                            <a href="ftp_management.php" class="nav-link">Go to FTP Management</a>
                            <a href="dashboard.php" class="nav-link">Return to Dashboard</a>
                        </div>
                    </div>
                <?php else: ?>
                    <div style="background: #fef3c7; border-radius: 8px; padding: 1.5rem; margin-bottom: 2rem;">
                        <h3 style="color: #92400e; margin-bottom: 1rem;">Setup Required</h3>
                        <p style="color: #a16207; margin-bottom: 1rem;">
                            Some database tables are missing. Click the button below to create the required tables and initialize the FTP management system.
                        </p>
                        <ul style="color: #a16207; margin-left: 1.5rem;">
                            <li>Create FTP server configuration tables</li>
                            <li>Create company FTP assignment tables</li>
                            <li>Create system settings tables</li>
                            <li>Add FTP-related columns to existing tables</li>
                            <li>Insert default configuration values</li>
                        </ul>
                    </div>

                    <form method="POST" class="setup-actions">
                        <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                        <button type="submit" name="setup_tables" class="btn-setup"
                                onclick="return confirm('This will create new database tables and modify existing ones. Continue?')">
                            Initialize FTP Management Tables
                        </button>
                    </form>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </main>
</body>
</html>
