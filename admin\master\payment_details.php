<?php
/**
 * Payment Details Page
 * Master Admin interface for viewing detailed payment information
 */

define('ADMIN_ACCESS', true);
require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../shared/admin_config.php';

// Require login and master admin privileges
requireLogin();
requireAdminAccess('master');

$database = new Database();
$db = $database->getConnection();

$error_message = '';
$success_message = '';
$payment = null;

// Get payment ID from URL parameter
$payment_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($payment_id <= 0) {
    $error_message = 'Invalid payment ID provided.';
} else {
    // Get detailed payment information with related data
    $query = "SELECT p.*, 
                     c.company_name, c.company_code, c.address as company_address, 
                     c.telephone as company_telephone, c.email as company_email,
                     c.company_reg_no, c.vat_no, c.customs_code,
                     u.name as user_name, u.email as user_email, u.role as user_role,
                     creator.name as created_by_name
              FROM payments p 
              LEFT JOIN companies c ON p.company_id = c.id 
              LEFT JOIN users u ON p.user_id = u.id 
              LEFT JOIN users creator ON p.user_id = creator.id
              WHERE p.id = ?";
    
    $stmt = $db->prepare($query);
    $stmt->execute([$payment_id]);
    $payment = $stmt->fetch();
    
    if (!$payment) {
        $error_message = 'Payment not found.';
    }
}

// Handle payment status updates
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $payment) {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        $error_message = 'Invalid security token. Please try again.';
    } else {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'approve':
                $query = "UPDATE payments SET payment_status = 'completed', updated_at = NOW() WHERE id = ?";
                $stmt = $db->prepare($query);
                if ($stmt->execute([$payment_id])) {
                    $success_message = 'Payment approved successfully.';
                    logActivity('payment_approved', "Payment ID {$payment_id} approved", $_SESSION['user_id']);
                    // Refresh payment data
                    $refresh_query = "SELECT p.*, 
                                             c.company_name, c.company_code, c.address as company_address, 
                                             c.telephone as company_telephone, c.email as company_email,
                                             c.company_reg_no, c.vat_no, c.customs_code,
                                             u.name as user_name, u.email as user_email, u.role as user_role,
                                             creator.name as created_by_name
                                      FROM payments p 
                                      LEFT JOIN companies c ON p.company_id = c.id 
                                      LEFT JOIN users u ON p.user_id = u.id 
                                      LEFT JOIN users creator ON p.user_id = creator.id
                                      WHERE p.id = ?";
                    $refresh_stmt = $db->prepare($refresh_query);
                    $refresh_stmt->execute([$payment_id]);
                    $payment = $refresh_stmt->fetch();
                } else {
                    $error_message = 'Failed to approve payment.';
                }
                break;
                
            case 'reject':
                $query = "UPDATE payments SET payment_status = 'failed', updated_at = NOW() WHERE id = ?";
                $stmt = $db->prepare($query);
                if ($stmt->execute([$payment_id])) {
                    $success_message = 'Payment rejected successfully.';
                    logActivity('payment_rejected', "Payment ID {$payment_id} rejected", $_SESSION['user_id']);
                    // Refresh payment data
                    $refresh_query = "SELECT p.*, 
                                             c.company_name, c.company_code, c.address as company_address, 
                                             c.telephone as company_telephone, c.email as company_email,
                                             c.company_reg_no, c.vat_no, c.customs_code,
                                             u.name as user_name, u.email as user_email, u.role as user_role,
                                             creator.name as created_by_name
                                      FROM payments p 
                                      LEFT JOIN companies c ON p.company_id = c.id 
                                      LEFT JOIN users u ON p.user_id = u.id 
                                      LEFT JOIN users creator ON p.user_id = creator.id
                                      WHERE p.id = ?";
                    $refresh_stmt = $db->prepare($refresh_query);
                    $refresh_stmt->execute([$payment_id]);
                    $payment = $refresh_stmt->fetch();
                } else {
                    $error_message = 'Failed to reject payment.';
                }
                break;
        }
    }
}

// Get related activity logs for this payment
$activity_logs = [];
if ($payment) {
    $activity_query = "SELECT al.*, u.name as user_name 
                       FROM activity_logs al 
                       LEFT JOIN users u ON al.user_id = u.id 
                       WHERE al.description LIKE ? 
                       ORDER BY al.created_at DESC 
                       LIMIT 10";
    $activity_stmt = $db->prepare($activity_query);
    $activity_stmt->execute(["%Payment ID {$payment_id}%"]);
    $activity_logs = $activity_stmt->fetchAll();
}

$csrf_token = generateCSRFToken();
$page_title = $payment ? "Payment Details - #" . str_pad($payment['id'], 6, '0', STR_PAD_LEFT) : "Payment Not Found";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo APP_NAME; ?></title>
    <link rel="icon" type="image/x-icon" href="../../assets/img/favicon.ico">
    <?php echo generateAdminCSS('master'); ?>
    <style>
        .payment-details-container {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .payment-main-info {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(43, 94, 95, 0.08);
        }
        
        .payment-sidebar {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }
        
        .info-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(43, 94, 95, 0.08);
        }
        
        .payment-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 2rem;
            padding-bottom: 1.5rem;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .payment-id {
            font-size: 2rem;
            font-weight: 700;
            color: #2B5E5F;
            font-family: 'Courier New', monospace;
        }
        
        .payment-status {
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-completed {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }
        
        .status-failed {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .status-processing {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .status-cancelled {
            background: #f3f4f6;
            color: #374151;
        }
        
        .payment-amount {
            font-size: 2.5rem;
            font-weight: 700;
            color: #065f46;
            margin: 1rem 0;
        }
        
        .detail-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .detail-item {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .detail-label {
            font-weight: 600;
            color: #2B5E5F;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .detail-value {
            font-size: 1rem;
            color: #374151;
            word-wrap: break-word;
        }
        
        .detail-value.highlight {
            font-weight: 600;
            color: #2B5E5F;
        }
        
        .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2B5E5F;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .action-buttons {
            display: flex;
            gap: 1rem;
            margin-top: 2rem;
            flex-wrap: wrap;
        }
        
        .btn-approve {
            background: #10b981;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .btn-approve:hover {
            background: #059669;
        }
        
        .btn-reject {
            background: #ef4444;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .btn-reject:hover {
            background: #dc2626;
        }
        
        .activity-log {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .activity-item {
            padding: 1rem;
            border-left: 3px solid #2B5E5F;
            margin-bottom: 1rem;
            background: #f8fafa;
            border-radius: 0 8px 8px 0;
        }
        
        .activity-time {
            font-size: 0.85rem;
            color: #666;
            margin-bottom: 0.5rem;
        }
        
        .activity-description {
            font-weight: 500;
            color: #374151;
        }
        
        .activity-user {
            font-size: 0.85rem;
            color: #666;
            margin-top: 0.5rem;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
        }
        
        .alert-success {
            background: #d1fae5;
            border: 1px solid #10b981;
            color: #065f46;
        }
        
        .alert-error {
            background: #fee2e2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }
        
        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: #2B5E5F;
            text-decoration: none;
            font-weight: 600;
            margin-bottom: 1.5rem;
        }
        
        .back-link:hover {
            color: #1a4344;
        }
        
        @media (max-width: 768px) {
            .payment-details-container {
                grid-template-columns: 1fr;
            }
            
            .payment-header {
                flex-direction: column;
                gap: 1rem;
                align-items: flex-start;
            }
            
            .detail-grid {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <?php echo generateAdminHeader($page_title, 'master', 'payments.php'); ?>

    <main class="main-content">
        <div class="page-header">
            <a href="payments.php" class="back-link">
                ← Back to Payments
            </a>
            <h1 class="page-title"><?php echo $page_title; ?></h1>
            <p class="page-subtitle">Detailed payment information and management</p>
        </div>

        <?php if ($error_message): ?>
            <div class="alert alert-error">
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>

        <?php if ($success_message): ?>
            <div class="alert alert-success">
                <?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>

        <?php if ($payment): ?>
            <div class="payment-details-container">
                <!-- Main Payment Information -->
                <div class="payment-main-info">
                    <div class="payment-header">
                        <div>
                            <div class="payment-id">#<?php echo str_pad($payment['id'], 6, '0', STR_PAD_LEFT); ?></div>
                            <div class="payment-amount">R<?php echo number_format($payment['amount'], 2); ?></div>
                        </div>
                        <div class="payment-status status-<?php echo $payment['payment_status']; ?>">
                            <?php echo ucfirst($payment['payment_status']); ?>
                        </div>
                    </div>

                    <div class="detail-grid">
                        <div class="detail-item">
                            <div class="detail-label">Invoice Name</div>
                            <div class="detail-value highlight"><?php echo htmlspecialchars($payment['invoice_name']); ?></div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">Payment Gateway</div>
                            <div class="detail-value"><?php echo htmlspecialchars($payment['payment_gateway']); ?></div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">Payment Reference</div>
                            <div class="detail-value"><?php echo $payment['payment_reference'] ? htmlspecialchars($payment['payment_reference']) : 'N/A'; ?></div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">Gateway Transaction ID</div>
                            <div class="detail-value"><?php echo $payment['gateway_transaction_id'] ? htmlspecialchars($payment['gateway_transaction_id']) : 'N/A'; ?></div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">Payment Date</div>
                            <div class="detail-value">
                                <?php
                                if ($payment['payment_date']) {
                                    echo date('F j, Y \a\t H:i:s', strtotime($payment['payment_date']));
                                } else {
                                    echo 'Not completed';
                                }
                                ?>
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">Created Date</div>
                            <div class="detail-value"><?php echo date('F j, Y \a\t H:i:s', strtotime($payment['created_at'])); ?></div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">Last Updated</div>
                            <div class="detail-value"><?php echo date('F j, Y \a\t H:i:s', strtotime($payment['updated_at'])); ?></div>
                        </div>
                    </div>

                    <?php if ($payment['gateway_response']): ?>
                        <div class="detail-item">
                            <div class="detail-label">Gateway Response</div>
                            <div class="detail-value" style="background: #f8f9fa; padding: 1rem; border-radius: 8px; font-family: monospace; white-space: pre-wrap; max-height: 200px; overflow-y: auto;">
                                <?php echo htmlspecialchars($payment['gateway_response']); ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Action Buttons -->
                    <?php if ($payment['payment_status'] === 'pending'): ?>
                        <div class="action-buttons">
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                                <input type="hidden" name="action" value="approve">
                                <button type="submit" class="btn-approve" onclick="return confirm('Are you sure you want to approve this payment?')">
                                    Approve Payment
                                </button>
                            </form>

                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                                <input type="hidden" name="action" value="reject">
                                <button type="submit" class="btn-reject" onclick="return confirm('Are you sure you want to reject this payment?')">
                                    Reject Payment
                                </button>
                            </form>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Sidebar Information -->
                <div class="payment-sidebar">
                    <!-- Company Information -->
                    <?php if ($payment['company_name']): ?>
                        <div class="info-card">
                            <div class="card-title">Company Information</div>
                            <div class="detail-grid">
                                <div class="detail-item">
                                    <div class="detail-label">Company Name</div>
                                    <div class="detail-value highlight"><?php echo htmlspecialchars($payment['company_name']); ?></div>
                                </div>

                                <div class="detail-item">
                                    <div class="detail-label">Company Code</div>
                                    <div class="detail-value"><?php echo htmlspecialchars($payment['company_code']); ?></div>
                                </div>

                                <?php if ($payment['company_reg_no']): ?>
                                    <div class="detail-item">
                                        <div class="detail-label">Registration No</div>
                                        <div class="detail-value"><?php echo htmlspecialchars($payment['company_reg_no']); ?></div>
                                    </div>
                                <?php endif; ?>

                                <?php if ($payment['vat_no']): ?>
                                    <div class="detail-item">
                                        <div class="detail-label">VAT Number</div>
                                        <div class="detail-value"><?php echo htmlspecialchars($payment['vat_no']); ?></div>
                                    </div>
                                <?php endif; ?>

                                <?php if ($payment['customs_code']): ?>
                                    <div class="detail-item">
                                        <div class="detail-label">Customs Code</div>
                                        <div class="detail-value"><?php echo htmlspecialchars($payment['customs_code']); ?></div>
                                    </div>
                                <?php endif; ?>

                                <?php if ($payment['company_email']): ?>
                                    <div class="detail-item">
                                        <div class="detail-label">Email</div>
                                        <div class="detail-value"><?php echo htmlspecialchars($payment['company_email']); ?></div>
                                    </div>
                                <?php endif; ?>

                                <?php if ($payment['company_telephone']): ?>
                                    <div class="detail-item">
                                        <div class="detail-label">Telephone</div>
                                        <div class="detail-value"><?php echo htmlspecialchars($payment['company_telephone']); ?></div>
                                    </div>
                                <?php endif; ?>

                                <?php if ($payment['company_address']): ?>
                                    <div class="detail-item">
                                        <div class="detail-label">Address</div>
                                        <div class="detail-value"><?php echo nl2br(htmlspecialchars($payment['company_address'])); ?></div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- User Information -->
                    <?php if ($payment['user_name']): ?>
                        <div class="info-card">
                            <div class="card-title">User Information</div>
                            <div class="detail-grid">
                                <div class="detail-item">
                                    <div class="detail-label">User Name</div>
                                    <div class="detail-value highlight"><?php echo htmlspecialchars($payment['user_name']); ?></div>
                                </div>

                                <div class="detail-item">
                                    <div class="detail-label">Email</div>
                                    <div class="detail-value"><?php echo htmlspecialchars($payment['user_email']); ?></div>
                                </div>

                                <div class="detail-item">
                                    <div class="detail-label">Role</div>
                                    <div class="detail-value"><?php echo ucfirst(str_replace('_', ' ', $payment['user_role'])); ?></div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Activity Log -->
                    <?php if (!empty($activity_logs)): ?>
                        <div class="info-card">
                            <div class="card-title">Related Activity</div>
                            <div class="activity-log">
                                <?php foreach ($activity_logs as $log): ?>
                                    <div class="activity-item">
                                        <div class="activity-time"><?php echo date('M j, Y H:i:s', strtotime($log['created_at'])); ?></div>
                                        <div class="activity-description"><?php echo htmlspecialchars($log['description']); ?></div>
                                        <?php if ($log['user_name']): ?>
                                            <div class="activity-user">by <?php echo htmlspecialchars($log['user_name']); ?></div>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php else: ?>
            <div class="admin-card" style="text-align: center; padding: 3rem;">
                <h2 style="color: #666; margin-bottom: 1rem;">Payment Not Found</h2>
                <p style="color: #999; margin-bottom: 2rem;">The requested payment could not be found or you don't have permission to view it.</p>
                <a href="payments.php" class="btn">Back to Payments</a>
            </div>
        <?php endif; ?>
    </main>
</body>
</html>
